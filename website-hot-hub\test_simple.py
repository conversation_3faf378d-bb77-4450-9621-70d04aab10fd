#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试脚本 - 验证项目基本功能
"""

import json
import os
from datetime import datetime

def test_data_structure():
    """测试数据结构"""
    print("🔍 测试数据结构...")
    
    # 检查raw目录结构
    raw_dir = "raw"
    if os.path.exists(raw_dir):
        platforms = os.listdir(raw_dir)
        print(f"✅ 发现 {len(platforms)} 个平台: {', '.join(platforms)}")
        
        # 检查每个平台的数据
        for platform in platforms:
            platform_dir = os.path.join(raw_dir, platform)
            if os.path.isdir(platform_dir):
                files = [f for f in os.listdir(platform_dir) if f.endswith('.json')]
                print(f"📊 {platform}: {len(files)} 个数据文件")
                
                # 检查最新的数据文件
                if files:
                    latest_file = sorted(files)[-1]
                    file_path = os.path.join(platform_dir, latest_file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:
                                data = json.loads(content)
                                print(f"   📝 {latest_file}: {len(data)} 条记录")
                                if data and isinstance(data, list) and len(data) > 0:
                                    sample = data[0]
                                    print(f"   🔍 示例数据字段: {list(sample.keys())}")
                            else:
                                print(f"   ⚠️  {latest_file}: 文件为空")
                    except Exception as e:
                        print(f"   ❌ {latest_file}: 读取失败 - {e}")
    else:
        print("❌ raw目录不存在")

def test_archives():
    """测试归档数据"""
    print("\n📚 测试归档数据...")
    
    archives_dir = "archives"
    if os.path.exists(archives_dir):
        platforms = os.listdir(archives_dir)
        print(f"✅ 发现 {len(platforms)} 个平台归档")
        
        for platform in platforms:
            platform_dir = os.path.join(archives_dir, platform)
            if os.path.isdir(platform_dir):
                files = [f for f in os.listdir(platform_dir) if f.endswith('.md')]
                print(f"📄 {platform}: {len(files)} 个归档文件")
    else:
        print("❌ archives目录不存在")

def test_readme():
    """测试README文件"""
    print("\n📖 测试README文件...")
    
    if os.path.exists("README.md"):
        with open("README.md", 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查各平台数据块
        platforms = ['36KR', 'BILIBILI', 'GITHUB', 'DOUYIN', 'JUEJIN', 'WEREAD', 'KUAISHOU', 'SSPAI']
        
        for platform in platforms:
            begin_marker = f"<!-- BEGIN {platform} -->"
            end_marker = f"<!-- END {platform} -->"
            
            if begin_marker in content and end_marker in content:
                start = content.find(begin_marker)
                end = content.find(end_marker)
                if start < end:
                    section = content[start:end]
                    lines = [line for line in section.split('\n') if line.strip().startswith('1.')]
                    print(f"✅ {platform}: {len(lines)} 条热点数据")
                else:
                    print(f"❌ {platform}: 数据块格式错误")
            else:
                print(f"⚠️  {platform}: 未找到数据块")
    else:
        print("❌ README.md文件不存在")

def show_sample_data():
    """显示示例数据"""
    print("\n🎯 显示示例数据...")
    
    # 显示GitHub最新数据
    github_file = "raw/github/2025-08-02.json"
    if os.path.exists(github_file):
        with open(github_file, 'r', encoding='utf-8') as f:
            data = json.loads(f.read())
            print(f"📊 GitHub热门项目 (共{len(data)}个):")
            for i, item in enumerate(data[:5], 1):
                print(f"   {i}. {item['title']} ({item['language']}) ⭐{item['stars']}")
                print(f"      {item['description'][:80]}...")
    
    # 显示36Kr最新数据
    kr_file = "raw/36kr/2025-08-02.json"
    if os.path.exists(kr_file):
        with open(kr_file, 'r', encoding='utf-8') as f:
            data = json.loads(f.read())
            print(f"\n📰 36Kr热点文章 (共{len(data)}个):")
            for i, item in enumerate(data[:3], 1):
                print(f"   {i}. {item['title']}")

def main():
    """主函数"""
    print("🚀 Website Hot Hub 项目测试")
    print("=" * 50)
    
    test_data_structure()
    test_archives()
    test_readme()
    show_sample_data()
    
    print("\n" + "=" * 50)
    print("✨ 测试完成！")

if __name__ == "__main__":
    main()
